from openpyxl import load_workbook
from openpyxl.utils import range_boundaries
from deep_translator import GoogleTranslator

# Load workbook
wb = load_workbook('/Users/<USER>/workspace/my-docs/project/demo.xlsx')
translator = GoogleTranslator(source='en', target='ja')

# Iterate sheets
for sheet in wb.worksheets:
    # Get all table ranges
    table_ranges = [tbl.ref for tbl in sheet.tables.values()]  # Correct API

    # Function to check if a cell is in a table header
    def is_in_table_header(cell):
        for table_ref in table_ranges:
            min_col, min_row, max_col, max_row = range_boundaries(table_ref)
            if cell.row == min_row and min_col <= cell.column <= max_col:
                return True
        return False

    for row in sheet.iter_rows():
        for cell in row:
            if isinstance(cell.value, str):
                # Translate ALL text cells, including headers
                try:
                    cell.value = translator.translate(cell.value)
                except Exception as e:
                    print(f"Translation failed at {cell.coordinate}: {e}")

# Save the translated file
wb.save('/Users/<USER>/workspace/my-docs/project/demo_gen.xlsx')
