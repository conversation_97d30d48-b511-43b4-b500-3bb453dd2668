from openpyxl import load_workbook
from deep_translator import GoogleTranslator
import shutil

# Make a backup of the original file
original_file = '/Users/<USER>/workspace/my-docs/project/demo.xlsx'
backup_file = '/Users/<USER>/workspace/my-docs/project/demo_backup.xlsx'
shutil.copy2(original_file, backup_file)
print(f"Created backup at {backup_file}")

# Load workbook
wb = load_workbook(original_file)
translator = GoogleTranslator(source='en', target='ja')

# Remove all tables to avoid corruption when translating headers
for sheet in wb.worksheets:
    # Remove all tables from the sheet
    tables_to_remove = list(sheet.tables.keys())
    for table_name in tables_to_remove:
        del sheet.tables[table_name]
    print(f"Removed {len(tables_to_remove)} table(s) from sheet '{sheet.title}'")

# Now translate all text cells including headers
for sheet in wb.worksheets:
    for row in sheet.iter_rows():
        for cell in row:
            if isinstance(cell.value, str):
                try:
                    original_value = cell.value
                    translated_value = translator.translate(original_value)
                    cell.value = translated_value
                    print(f"Translated {cell.coordinate}: '{original_value}' -> '{translated_value}'")
                except Exception as e:
                    print(f"Translation failed at {cell.coordinate}: {e}")

# Save the final file
output_file = '/Users/<USER>/workspace/my-docs/project/demo_gen.xlsx'
wb.save(output_file)
print(f"Saved final translated file to {output_file}")

print("✅ Translation completed successfully! Headers and data are both translated.")
