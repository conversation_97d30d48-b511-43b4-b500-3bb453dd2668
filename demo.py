from openpyxl import load_workbook, Workbook
from deep_translator import GoogleTranslator
import shutil

# File paths
original_file = '/Users/<USER>/workspace/my-docs/project/demo.xlsx'
backup_file = '/Users/<USER>/workspace/my-docs/project/demo_backup.xlsx'
output_file = '/Users/<USER>/workspace/my-docs/project/demo_gen.xlsx'

# 1. Make a backup of the original file
shutil.copy2(original_file, backup_file)
print(f"✅ Created backup at {backup_file}")

# 2. Load workbook
wb = load_workbook(original_file)
translator = GoogleTranslator(source='en', target='ja')

# 3. Remove all table definitions to avoid corruption
for sheet in wb.worksheets:
    tables_to_remove = list(sheet.tables.keys())
    for table_name in tables_to_remove:
        del sheet.tables[table_name]
    print(f"🧹 Removed {len(tables_to_remove)} table(s) from sheet '{sheet.title}'")

# 4. Translate all string cells (headers + content)
for sheet in wb.worksheets:
    for row in sheet.iter_rows():
        for cell in row:
            if isinstance(cell.value, str):
                try:
                    original_value = cell.value.strip()
                    if original_value:  # skip empty strings
                        translated_value = translator.translate(original_value)
                        cell.value = translated_value
                        print(f"🌐 {cell.coordinate}: '{original_value}' → '{translated_value}'")
                except Exception as e:
                    print(f"❌ Translation failed at {cell.coordinate}: {e}")

# 5. Create a clean workbook and copy translated data
clean_wb = Workbook()
clean_wb.remove(clean_wb.active)  # Remove default sheet

for sheet in wb.worksheets:
    new_sheet = clean_wb.create_sheet(title=sheet.title)
    for row in sheet.iter_rows():
        for cell in row:
            new_cell = new_sheet.cell(row=cell.row, column=cell.column)
            new_cell.value = cell.value  # Copy only the translated values

# 6. Save the final clean and translated file
clean_wb.save(output_file)
print(f"✅ Saved translated file to {output_file}")
print("🎉 Translation completed successfully! No Excel repair warnings expected.")
