from openpyxl import load_workbook
from openpyxl.utils import range_boundaries

print("=== COMPARING ORIGINAL AND TRANSLATED FILES ===")

# Load both files
original_wb = load_workbook('/Users/<USER>/workspace/my-docs/project/demo.xlsx')
translated_wb = load_workbook('/Users/<USER>/workspace/my-docs/project/demo_gen.xlsx')

for sheet_name in original_wb.sheetnames:
    print(f"\n--- Sheet: {sheet_name} ---")

    original_sheet = original_wb[sheet_name]
    translated_sheet = translated_wb[sheet_name]

    # Get table ranges for this sheet
    table_ranges = [tbl.ref for tbl in original_sheet.tables.values()]

    # Function to check if a cell is in a table header
    def is_in_table_header(cell):
        for table_ref in table_ranges:
            min_col, min_row, max_col, max_row = range_boundaries(table_ref)
            if cell.row == min_row and min_col <= cell.column <= max_col:
                return True
        return False

    print("Comparing cells:")
    for row in range(1, 7):  # Check first 6 rows
        for col in range(1, 3):  # Check first 2 columns
            orig_cell = original_sheet.cell(row=row, column=col)
            trans_cell = translated_sheet.cell(row=row, column=col)

            if orig_cell.value:
                is_header = is_in_table_header(orig_cell)
                status = "HEADER" if is_header else "DATA"

                print(f"  {orig_cell.coordinate} ({status}): '{orig_cell.value}' -> '{trans_cell.value}'")

                # Check if translation was applied correctly
                if is_header and orig_cell.value != trans_cell.value:
                    print(f"    ⚠️  WARNING: Header was translated (should be skipped)")
                elif not is_header and orig_cell.value == trans_cell.value:
                    print(f"    ⚠️  WARNING: Data was not translated")

print("\n=== END COMPARISON ===")